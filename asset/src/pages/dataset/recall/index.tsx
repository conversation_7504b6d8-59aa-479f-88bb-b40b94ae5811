import { Content, RequestButton, Space, useRequest } from '@topthink/common';
import { useDataset } from '../provider';
import { useCurrentSpace } from '@/components/space-provider';
import { useState } from 'react';
import { <PERSON><PERSON>, Badge, Card, Col, Form, Row } from 'react-bootstrap';

interface RecallResult {
    score: number;
    content: string;
    title: string;
    source_id: number | null;
}

interface RecallResponse {
    query: string;
    limit: number;
    results: RecallResult[];
    total: number;
}

export const Component = function() {
    const { current: space } = useCurrentSpace();
    const { current: dataset } = useDataset();
    const [query, setQuery] = useState('');
    const [limit, setLimit] = useState(5);
    const [results, setResults] = useState<RecallResponse | null>(null);
    const [loading, setLoading] = useState(false);

    const handleTest = async () => {
        if (!query.trim()) {
            return;
        }

        setLoading(true);
        try {
            const response = await fetch(`/api/space/${space.hash_id}/dataset/${dataset.hash_id}/recall`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                },
                body: JSON.stringify({
                    query: query.trim(),
                    limit: limit,
                }),
            });

            if (response.ok) {
                const data = await response.json();
                setResults(data);
            } else {
                console.error('召回测试失败');
            }
        } catch (error) {
            console.error('召回测试出错:', error);
        } finally {
            setLoading(false);
        }
    };

    const getScoreColor = (score: number) => {
        if (score >= 0.8) return 'success';
        if (score >= 0.6) return 'warning';
        return 'danger';
    };

    return <Content>
        <Card className="mb-4">
            <Card.Header>
                <h5 className="mb-0">召回测试</h5>
            </Card.Header>
            <Card.Body>
                <Form>
                    <Row className="g-3">
                        <Col md={8}>
                            <Form.Group>
                                <Form.Label>查询内容</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="请输入要测试的查询内容..."
                                    value={query}
                                    onChange={(e) => setQuery(e.target.value)}
                                    onKeyPress={(e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            handleTest();
                                        }
                                    }}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>返回数量</Form.Label>
                                <Form.Select
                                    value={limit}
                                    onChange={(e) => setLimit(parseInt(e.target.value))}
                                >
                                    <option value={3}>3</option>
                                    <option value={5}>5</option>
                                    <option value={10}>10</option>
                                    <option value={20}>20</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>&nbsp;</Form.Label>
                                <div className="d-grid">
                                    <RequestButton
                                        variant="primary"
                                        disabled={!query.trim() || loading}
                                        onClick={handleTest}
                                    >
                                        {loading ? '测试中...' : '开始测试'}
                                    </RequestButton>
                                </div>
                            </Form.Group>
                        </Col>
                    </Row>
                </Form>
            </Card.Body>
        </Card>

        {results && (
            <Card>
                <Card.Header>
                    <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">测试结果</h6>
                        <Badge bg="secondary">共找到 {results.total} 条结果</Badge>
                    </div>
                </Card.Header>
                <Card.Body>
                    {results.results.length === 0 ? (
                        <Alert variant="info">
                            未找到相关内容，请尝试其他查询词。
                        </Alert>
                    ) : (
                        <Space direction="vertical" size="large" className="w-100">
                            {results.results.map((result, index) => (
                                <Card key={index} className="border">
                                    <Card.Body>
                                        <div className="d-flex justify-content-between align-items-start mb-2">
                                            <h6 className="mb-0">结果 #{index + 1}</h6>
                                            <Badge bg={getScoreColor(result.score)}>
                                                相似度: {(result.score * 100).toFixed(1)}%
                                            </Badge>
                                        </div>
                                        {result.title && (
                                            <div className="mb-2">
                                                <strong>标题:</strong> {result.title}
                                            </div>
                                        )}
                                        <div className="mb-2">
                                            <strong>内容:</strong>
                                        </div>
                                        <div 
                                            className="p-3 bg-light rounded"
                                            style={{ 
                                                whiteSpace: 'pre-wrap',
                                                maxHeight: '200px',
                                                overflowY: 'auto'
                                            }}
                                        >
                                            {result.content}
                                        </div>
                                        {result.source_id && (
                                            <div className="mt-2 text-muted small">
                                                来源ID: {result.source_id}
                                            </div>
                                        )}
                                    </Card.Body>
                                </Card>
                            ))}
                        </Space>
                    )}
                </Card.Body>
            </Card>
        )}
    </Content>;
};
